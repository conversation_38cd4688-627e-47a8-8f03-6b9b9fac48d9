
<?php
// Incluir el gestor de sesión centralizado
require_once 'includes/session_manager.php';

// Incluir la clase de conexión a la base de datos
require_once 'DatabaseConnection.php';

// Incluir configuración global
require_once 'GlobalConfig.php';
$globalConfig = GlobalConfig::getInstance();

// Obtener parámetros de sesión
$periodo = $globalConfig->get('periodo_actual');
$rutSesion = isset($_SESSION['rut']) ? $_SESSION['rut'] : '';
$idSesion = isset($_SESSION['id_sesion']) ? $_SESSION['id_sesion'] : '';

// Obtener el RutTecnicoOrig desde POST o de la sesión
$rutTecnico = isset($_POST['RutTecnicoOrig']) ? $_POST['RutTecnicoOrig'] : $rutSesion;

// Si no se proporciona el RUT, mostrar un mensaje de error
if (!$rutTecnico) {
    die("<div style='text-align:center; padding:20px;'>
            <h2>Error</h2>
            <p>No se ha proporcionado el RUT del técnico.</p>
            <p>Por favor, vuelva a intentarlo.</p>
         </div>");
}

// Obtener la conexión a la base de datos
$db = DatabaseConnection::getInstance();
$conn = $db->getConnection();

// Asegurar que el período tenga el formato correcto (YYYYMM)
$periodo = preg_replace('/[^0-9]/', '', $periodo); // Eliminar cualquier carácter que no sea número
if (strlen($periodo) == 6) {
    // Ya está en formato YYYYMM
} elseif (strlen($periodo) == 7 && strpos($periodo, '-') !== false) {
    // Convertir de YYYY-MM a YYYYMM
    $periodo = str_replace('-', '', $periodo);
} else {
    // Usar el período actual si el formato no es reconocido
    $periodo = date('Ym');
}

// Consultar los datos del técnico
$query = "SELECT RutTecnicoOrig, NombreTecnico, Supervisor, Zona_Factura23, modelo_turno, categoria,
Original_RUT_TECNICO, DIAS_BASE_DRIVE, SUM_OPERATIVO, Dias_Cantidad_HFC, Dias_Cantidad_FTTH,
Puntos, Q_RGU, Promedio_HFC, Promedio_RGU, Q_OPERATIVO_TURNO, Q_AUSENTE_TURNO, Q_VACACIONES_TURNO,
Q_LICENCIA_TURNO, FACTOR_AUSENCIA, FACTOR_VACACIONES, Meta_Produccion_FTTH, Meta_Produccion_HFC,
_cumplimientoProduccionRGU, _CumplimientoProduccionHFC, Ratio_CalidadFTTH, Ratio_CalidadHFC,
Q_Calidad30_FTTH, Q_Cantidad_FTTH, Q_Calidad30_HFC, Q_Cantidad_HFC, Meta_Calidad_FTTH,
Meta_Calidad_HFC, _cumplimientoMeta_Calidad_FTTH, _cumplimientoMeta_Calidad_HFC,
CalidadReactivaGrupoHFC, CalidadReactivaGrupoFTTH, Comisión_FTTH, Comisión_HFC,
Comisión_FTTH_Ponderada, Comisión_HFC_Ponderada
FROM tb_tqw_comision_renew
WHERE RutTecnicoOrig = ? AND periodo = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $rutTecnico, $periodo);
$stmt->execute();
$result = $stmt->get_result();

// Verificar si se encontraron datos
if ($result->num_rows > 0) {
    // Obtener los datos del técnico
    $tecnico = $result->fetch_assoc();
    
    // Asegurarse de que los campos existan
    $tecnico['NombreTecnico'] = $tecnico['NombreTecnico'] ?? 'Sin datos';
    $tecnico['Zona_Factura23'] = $tecnico['Zona_Factura23'] ?? 'N/A';
    $tecnico['modelo_turno'] = $tecnico['modelo_turno'] ?? 'N/A';
    $tecnico['categoria'] = $tecnico['categoria'] ?? 'N/A';
    $tecnico['Supervisor'] = $tecnico['Supervisor'] ?? '';
} else {
    // Si no hay resultados, crear un array con valores predeterminados
    $tecnico = [
        'RutTecnicoOrig' => $rutTecnico,
        'NombreTecnico' => 'Sin datos',
        'Supervisor' => '',
        'Zona_Factura23' => 'N/A',
        'modelo_turno' => 'N/A',
        'categoria' => 'N/A',
        'Original_RUT_TECNICO' => $rutTecnico,
        'DIAS_BASE_DRIVE' => 0,
        'SUM_OPERATIVO' => 0,
        'Dias_Cantidad_HFC' => 0,
        'Dias_Cantidad_FTTH' => 0,
        'Puntos' => 0,
        'Q_RGU' => 0,
        'Promedio_HFC' => 0,
        'Promedio_RGU' => 0,
        'Q_OPERATIVO_TURNO' => 0,
        'Q_AUSENTE_TURNO' => 0,
        'Q_VACACIONES_TURNO' => 0,
        'Q_LICENCIA_TURNO' => 0,
        'FACTOR_AUSENCIA' => 0,
        'FACTOR_VACACIONES' => 0,
        'Meta_Produccion_FTTH' => 0,
        'Meta_Produccion_HFC' => 0,
        '_cumplimientoProduccionRGU' => 0,
        '_CumplimientoProduccionHFC' => 0,
        'Ratio_CalidadFTTH' => 0,
        'Ratio_CalidadHFC' => 0,
        'Q_Calidad30_FTTH' => 0,
        'Q_Cantidad_FTTH' => 0,
        'Q_Calidad30_HFC' => 0,
        'Q_Cantidad_HFC' => 0,
        'Meta_Calidad_FTTH' => 0,
        'Meta_Calidad_HFC' => 0,
        '_cumplimientoMeta_Calidad_FTTH' => 0,
        '_cumplimientoMeta_Calidad_HFC' => 0,
        'CalidadReactivaGrupoHFC' => 0,
        'CalidadReactivaGrupoFTTH' => 0,
        'Comisión_FTTH' => 0,
        'Comisión_HFC' => 0,
        'Comisión_FTTH_Ponderada' => 0,
        'Comisión_HFC_Ponderada' => 0
    ];
}

// Función para formatear RUT (agregar o quitar puntos y guión según corresponda)
function formatearRutParaBusqueda($rut, $conFormato = true) {
    // Si el RUT viene con puntos y guión, quitarlos
    $rutLimpio = str_replace(['.', '-'], '', $rut);
    
    if ($conFormato) {
        // Si el RUT limpio tiene más de 1 dígito, darle formato
        if (strlen($rutLimpio) > 1) {
            $rutLimpio = substr($rutLimpio, 0, -1) . '-' . substr($rutLimpio, -1);
            // Agregar puntos de miles
            $rutLimpio = number_format(substr($rutLimpio, 0, -2), 0, '', '.') . substr($rutLimpio, -2);
        }
    }
    
    return $rutLimpio;
}

// Consulta de documentos pendientes en HCM
$pendingDocs = [];
$rutFormateado = formatearRutParaBusqueda($rutTecnico, true);
$rutSinFormato = formatearRutParaBusqueda($rutTecnico, false);

// Intentar con diferentes formatos de RUT
$formatosRut = [
    $rutFormateado,  // Con puntos y guión
    $rutSinFormato,  // Sin formato
    str_replace('.', '', $rutFormateado),  // Sin puntos, con guión
    str_replace('-', '', $rutSinFormato)   // Sin puntos ni guión
];

// Eliminar duplicados
$formatosRut = array_unique($formatosRut);

$debugInfo = [
    'rutTecnico' => $rutTecnico,
    'formatosIntentados' => $formatosRut,
    'query' => null,
    'error' => null,
    'rowCount' => 0,
    'pendingDocs' => []
];

foreach ($formatosRut as $formato) {
    $docQuery = "SELECT `Certificados/Documentos` AS documentos 
                FROM tb_hcmfront_doc_pendientes 
                WHERE `Número de Identificación` = ?";
    
    $docStmt = $conn->prepare($docQuery);
    if (!$docStmt) {
        $debugInfo['error'] = 'Error en prepare: ' . $conn->error;
        continue;
    }
    
    $docStmt->bind_param("s", $formato);
    $executed = $docStmt->execute();
    
    if (!$executed) {
        $debugInfo['error'] = 'Error en execute: ' . $docStmt->error;
        continue;
    }
    
    $docRes = $docStmt->get_result();
    if (!$docRes) {
        $debugInfo['error'] = 'Error en get_result';
        continue;
    }
    
    $rowCount = 0;
    while ($row = $docRes->fetch_assoc()) {
        if (!empty($row['documentos'])) {
            $pendingDocs[] = $row['documentos'];
            $rowCount++;
        }
    }
    
    if ($rowCount > 0) {
        // Si encontramos resultados, salir del bucle
        $debugInfo['rowCount'] = $rowCount;
        $debugInfo['formatoExitoso'] = $formato;
        break;
    }
    
    $docStmt->close();
}

$debugInfo['pendingDocs'] = $pendingDocs;
$debugInfo['rutFormateado'] = $rutFormateado;
$debugInfo['rutSinFormato'] = $rutSinFormato;

// Obtener el ID del usuario para el footer condicional
$id_usuario = null;
$userQuery = "SELECT id FROM tb_user_tqw WHERE rut = ?";
$userStmt = $conn->prepare($userQuery);
$userStmt->bind_param("s", $rutTecnico);
$userStmt->execute();
$userResult = $userStmt->get_result();
if ($userRow = $userResult->fetch_assoc()) {
    $id_usuario = $userRow['id'];
}

// Verificar si el usuario es parte del piloto del módulo logístico
$top_technicians_pilot = [1152, 1312, 233, 1218, 1153, 1223, 1246, 1150, 1301, 1211];
$is_pilot_user = isset($id_usuario) && in_array($id_usuario, $top_technicians_pilot);

// Formatear valores para mostrar
$comisionHFC = isset($tecnico['Comisión_HFC']) ? $tecnico['Comisión_HFC'] : '0';
$comisionFTTH = isset($tecnico['Comisión_FTTH']) ? $tecnico['Comisión_FTTH'] : '0';

// Calcular comisión total
$comisionTotal = 0;
if (is_numeric($comisionHFC)) {
    $comisionTotal += intval($comisionHFC);
}
if (is_numeric($comisionFTTH)) {
    $comisionTotal += intval($comisionFTTH);
}

// Formatear porcentajes
$cumplimientoHFC = isset($tecnico['_CumplimientoProduccionHFC']) ? $tecnico['_CumplimientoProduccionHFC'] : 0;
$cumplimientoFTTH = isset($tecnico['_cumplimientoProduccionRGU']) ? $tecnico['_cumplimientoProduccionRGU'] : 0;
$cumplimientoCalidadHFC = isset($tecnico['_cumplimientoMeta_Calidad_HFC']) ? $tecnico['_cumplimientoMeta_Calidad_HFC'] : 0;
$cumplimientoCalidadFTTH = isset($tecnico['_cumplimientoMeta_Calidad_FTTH']) ? $tecnico['_cumplimientoMeta_Calidad_FTTH'] : 0;

// Formatear factores
$factorAusencia = isset($tecnico['FACTOR_AUSENCIA']) ? ($tecnico['FACTOR_AUSENCIA'] * 100) : 100;
$factorVacaciones = isset($tecnico['FACTOR_VACACIONES']) ? ($tecnico['FACTOR_VACACIONES'] * 100) : 100;

// Obtener el mes actual para mostrar en el período
$meses = [
    1 => 'ENERO', 2 => 'FEBRERO', 3 => 'MARZO', 4 => 'ABRIL',
    5 => 'MAYO', 6 => 'JUNIO', 7 => 'JULIO', 8 => 'AGOSTO',
    9 => 'SEPTIEMBRE', 10 => 'OCTUBRE', 11 => 'NOVIEMBRE', 12 => 'DICIEMBRE'
];
$mesActual = $meses[date('n')];
$anioActual = date('Y');
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Resumen indicadores producción</title>
  <link rel="stylesheet" href="css/activity_dashboard.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
  <link rel="stylesheet" href="css/floating-menu.css">
  <link rel="stylesheet" href="css/metric-cards.css">
  <link rel="stylesheet" href="css/collapsible-sections.css">
  <link rel="stylesheet" href="css/form_panels.css">
  <link rel="stylesheet" href="css/logout-modal.css">
  <link rel="stylesheet" href="css/footer-fix-corrected.css"><!-- Corrección para el footer (versión corregida) -->
  <link rel="stylesheet" href="css/button-fix.css"><!-- Estandarización del botón + -->

  <!-- Estilos Modal HCM -->
  <style>
    .modal-overlay {
      position:fixed;
      top:0;
      left:0;
      width:100%;
      height:100%;
      background:rgba(0,0,0,.6);
      display:none;
      align-items:center;
      justify-content:center;
      z-index:1000;
      backdrop-filter: blur(3px);
    }
    .modal-content {
      background: linear-gradient(135deg, #2a3353 0%, #1e2746 100%);
      max-width:420px;
      width:90%;
      padding:25px 20px;
      border-radius:12px;
      position:relative;
      text-align:left;
      display:block;
      opacity:1;
      color:#fff;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    .close-modal {
      position:absolute;
      top:10px;
      right:15px;
      font-size:22px;
      cursor:pointer;
      color: #00e1fd;
      background: rgba(0, 225, 253, 0.1);
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    }
    .close-modal:hover {
      background: rgba(0, 225, 253, 0.2);
      transform: scale(1.05);
    }
    .modal-content a,
    .modal-content h3,
    .modal-content h4,
    .modal-content p,
    .modal-content li { color:#fff; }  /* aseguras todos */
    .modal-content h3 {
      margin-top:0;
      display:block;
      font-size: 1.4rem;
      font-weight: 600;
      letter-spacing: 0.5px;
      color: #00e1fd;
      margin-bottom: 10px;
      border-bottom: 1px solid rgba(0, 225, 253, 0.3);
      padding-bottom: 10px;
    }
    .modal-content p {
      display:block;
      margin:10px 0;
      line-height: 1.6;
    }
    .modal-content h4 {
      display:block;
      margin:15px 0 5px 0;
      color: rgba(255, 255, 255, 0.9);
      font-size: 1.1rem;
    }
    .modal-content ul {
      margin:10px 0 0 20px;
      padding:0;
      display:block;
    }
    .modal-content li {
      display:list-item;
      margin:8px 0;
      padding-left: 5px;
      position: relative;
    }
    .modal-content li::before {
      content: '•';
      color: #00e1fd;
      font-weight: bold;
      display: inline-block;
      width: 1em;
      margin-left: -1em;
    }

    /* Estilos para el sistema de dos mensajes */
    .modal-message {
      display: none;
    }
    .modal-message.active {
      display: block;
    }
    .modal-navigation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    .modal-btn {
      background: linear-gradient(135deg, #00e1fd 0%, #0099cc 100%);
      color: #fff;
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      font-size: 0.9rem;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 225, 253, 0.3);
    }
    .modal-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 225, 253, 0.4);
    }
    .modal-btn:disabled {
      background: rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.5);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    .modal-progress {
      display: flex;
      align-items: center;
      gap: 8px;
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.85rem;
    }
    .modal-progress-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transition: all 0.2s ease;
    }
    .modal-progress-dot.active {
      background: #00e1fd;
      box-shadow: 0 0 8px rgba(0, 225, 253, 0.5);
    }

    /* Estilos específicos para el modal del piloto */
    .pilot-highlight {
      background: linear-gradient(135deg, rgba(0, 225, 253, 0.15) 0%, rgba(0, 153, 204, 0.1) 100%);
      border: 1px solid rgba(0, 225, 253, 0.3);
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
      position: relative;
    }

    .pilot-highlight::before {
      content: '🚀';
      position: absolute;
      top: -8px;
      left: 15px;
      background: linear-gradient(135deg, #2a3353 0%, #1e2746 100%);
      padding: 0 8px;
      font-size: 1.2rem;
    }

    .pilot-link {
      display: inline-block;
      background: linear-gradient(135deg, #00e1fd 0%, #0099cc 100%);
      color: #fff !important;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 6px;
      margin-top: 15px;
      transition: all 0.2s ease;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(0, 225, 253, 0.3);
    }

    .pilot-link:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 225, 253, 0.4);
      text-decoration: none;
      color: #fff !important;
    }
  </style>
</head>
<body>
  <!-- Overlay para cerrar el menú flotante -->
  <div class="menu-overlay" id="menuOverlay"></div>
  <!-- Overlay para cerrar los formularios -->
  <div class="form-overlay" id="formOverlay"></div>

  <div class="app-container">
    <!-- Header Section -->


    <!-- Period Card -->
    <div class="period-card">
      <div class="period-content">
        <i class="bi bi-calendar3"></i>
        <h2>PERIODO <?php echo $periodo; ?></h2>
      </div>
    </div>
    <!-- Activity Cards -->
    <div class="activity-section">
      <!-- Información del Técnico -->
      <div class="activity-header collapsible-header">
        <div class="header-content">
          <h2>Información del Técnico</h2>
          <button type="button" class="collapse-btn" aria-label="Contraer/Expandir sección">
            <i class="bi bi-chevron-up"></i>
          </button>
        </div>
        <p class="activity-time" data-tecnico="<?php echo htmlspecialchars($tecnico['NombreTecnico']); ?>">Técnico: <?php echo htmlspecialchars($tecnico['NombreTecnico']); ?></p>
      </div>

      <div class="activity-metrics collapsible-content">

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-geo-alt"></i>
          </div>
          <div class="metric-value"><?php echo $tecnico['Zona_Factura23']; ?></div>
          <div class="metric-label">Zona</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-clock-history"></i>
          </div>
          <div class="metric-value"><?php echo $tecnico['modelo_turno']; ?></div>
          <div class="metric-label">Modelo Turno</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-star"></i>
          </div>
          <div class="metric-value"><?php echo $tecnico['categoria']; ?></div>
          <div class="metric-label">Categoría</div>
        </div>


      </div>

      <!-- Comisiones -->
      <div class="activity-header collapsible-header">
        <div class="header-content">
          <h2>Comisiones</h2>
          <button type="button" class="collapse-btn" aria-label="Contraer/Expandir sección">
            <i class="bi bi-chevron-up"></i>
          </button>
        </div>
      </div>

      <!-- Primera fila: Comisiones HFC y FTTH -->
      <div class="activity-metrics collapsible-content metas-grid">
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-cash"></i>
          </div>
          <div class="metric-value">$<?php echo number_format(intval($comisionHFC), 0, ',', '.'); ?></div>
          <div class="metric-label">Comisión HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-cash"></i>
          </div>
          <div class="metric-value">$<?php echo number_format(intval($comisionFTTH), 0, ',', '.'); ?></div>
          <div class="metric-label">Comisión FTTH</div>
        </div>
      </div>

      <!-- Segunda fila: Comisiones ponderadas y total -->
      <div class="activity-metrics collapsible-content">
        <?php
        // Manejo seguro de comisiones ponderadas
        $comisionHFCPonderada = isset($tecnico['Comisión_HFC_Ponderada']) ? $tecnico['Comisión_HFC_Ponderada'] : '0';
        $comisionFTTHPonderada = isset($tecnico['Comisión_FTTH_Ponderada']) ? $tecnico['Comisión_FTTH_Ponderada'] : '0';
        
        // Calcular total ponderado
        $comisionTotalPonderada = 0;
        if (is_numeric($comisionHFCPonderada)) {
            $comisionTotalPonderada += intval($comisionHFCPonderada);
        }
        if (is_numeric($comisionFTTHPonderada)) {
            $comisionTotalPonderada += intval($comisionFTTHPonderada);
        }
        ?>
        
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-cash"></i>
          </div>
          <div class="metric-value">$<?php echo number_format(intval($comisionHFCPonderada), 0, ',', '.'); ?></div>
          <div class="metric-label">Comisión HFC Ponderada</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-cash"></i>
          </div>
          <div class="metric-value">$<?php echo number_format(intval($comisionFTTHPonderada), 0, ',', '.'); ?></div>
          <div class="metric-label">Comisión FTTH Ponderada</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-cash-stack"></i>
          </div>
          <div class="metric-value">$<?php echo number_format($comisionTotalPonderada, 0, ',', '.'); ?></div>
          <div class="metric-label">Comisión Total Ponderada</div>
        </div>
      </div>

      <!-- Métricas de Producción -->
      <div class="activity-header collapsible-header">
        <div class="header-content">
          <h2>Producción</h2>
          <button type="button" class="collapse-btn" aria-label="Contraer/Expandir sección">
            <i class="bi bi-chevron-up"></i>
          </button>
        </div>
      </div>

      <div class="activity-metrics collapsible-content">
        <!-- HFC -->
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-bar-chart-line"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Puntos'], 0, ',', '.'); ?></div>
          <div class="metric-label">Puntos HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-calendar"></i>
          </div>
          <div class="metric-value"><?php echo intval($tecnico['Dias_Cantidad_HFC']); ?></div>
          <div class="metric-label">Cantidad Días HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-bullseye"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Promedio_HFC'], 2, '.', ','); ?></div>
          <div class="metric-label">Promedio HFC</div>
        </div>

        <!-- FTTH -->
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-bar-chart-line"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Q_RGU'], 0, ',', '.'); ?></div>
          <div class="metric-label">RGU FTTH</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-calendar"></i>
          </div>
          <div class="metric-value"><?php echo intval($tecnico['Dias_Cantidad_FTTH']); ?></div>
          <div class="metric-label">Cantidad Días  FTTH</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-bullseye"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Promedio_RGU'], 2, '.', ','); ?></div>
          <div class="metric-label">Promedio RGU</div>
        </div>
      </div>

      <!-- Metas y Cumplimiento en 2 columnas -->
      <div class="activity-metrics collapsible-content metas-grid">
          <!-- Fila 1: Metas de Producción -->
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Meta_Produccion_HFC'], 0, ',', '.'); ?></div>
          <div class="metric-label">Meta Producción HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-percent"></i>
          </div>
          <div class="metric-value"><?php echo number_format($cumplimientoHFC * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">Cumplimiento HFC</div>
        </div>

        <!-- Fila 2: Cumplimiento -->
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Meta_Produccion_FTTH'], 1, ',', '.'); ?></div>
          <div class="metric-label">Meta Producción FTTH</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-percent"></i>
          </div>
          <div class="metric-value"><?php echo number_format($cumplimientoFTTH * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">Cumplimiento FTTH</div>
        </div>
      </div>

      <!-- Métricas de Calidad -->
      <div class="activity-header collapsible-header">
        <div class="header-content">
          <h2>Indicadores de Calidad</h2>
          <button type="button" class="collapse-btn" aria-label="Contraer/Expandir sección">
            <i class="bi bi-chevron-up"></i>
          </button>
        </div>
      </div>

      <div class="activity-metrics collapsible-content">
        <!-- Fila para HFC -->
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-graph-up"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Ratio_CalidadHFC'] * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">KPI Calidad HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-check2-all"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Meta_Calidad_HFC'] * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">Meta Calidad HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-percent"></i>
          </div>
          <div class="metric-value"><?php echo number_format($cumplimientoCalidadHFC * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">Cumplimiento Calidad HFC</div>
        </div>

        <!-- Fila para FTTH -->
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-graph-up"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Ratio_CalidadFTTH'] * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">KPI Calidad FTTH</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-check2-all"></i>
          </div>
          <div class="metric-value"><?php echo number_format($tecnico['Meta_Calidad_FTTH'] * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">Meta Calidad FTTH</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-percent"></i>
          </div>
          <div class="metric-value"><?php echo number_format($cumplimientoCalidadFTTH * 100, 1, '.', ','); ?>%</div>
          <div class="metric-label">Cumplimiento Calidad FTTH</div>
        </div>

        <!-- Comentado: Calidad 30 HFC y FTTH
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-check2-circle"></i>
          </div>
          <div class="metric-value"><?php echo $tecnico['Q_Calidad30_HFC']; ?></div>
          <div class="metric-label">Calidad 30 HFC</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-check2-circle"></i>
          </div>
          <div class="metric-value"><?php echo $tecnico['Q_Calidad30_FTTH']; ?></div>
          <div class="metric-label">Calidad 30 FTTH</div>
        </div> -->
      </div>

      <!-- Métricas de Asistencia -->
      <div class="activity-header collapsible-header">
        <div class="header-content">
          <h2>Asistencia y Factores</h2>
          <button type="button" class="collapse-btn" aria-label="Contraer/Expandir sección">
            <i class="bi bi-chevron-up"></i>
          </button>
        </div>
      </div>

      <div class="activity-metrics collapsible-content">
        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-calendar-check"></i>
          </div>
          <div class="metric-value"><?php echo intval($tecnico['Q_OPERATIVO_TURNO']); ?></div>
          <div class="metric-label">Días Operativos</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-calendar-x"></i>
          </div>
          <div class="metric-value"><?php echo intval($tecnico['Q_AUSENTE_TURNO']); ?></div>
          <div class="metric-label">Días Ausente</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-calendar-heart"></i>
          </div>
          <div class="metric-value"><?php echo intval($tecnico['Q_VACACIONES_TURNO']); ?></div>
          <div class="metric-label">Días Vacaciones</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-calendar-plus"></i>
          </div>
          <div class="metric-value"><?php echo intval($tecnico['Q_LICENCIA_TURNO']); ?></div>
          <div class="metric-label">Días Licencia</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-person-x"></i>
          </div>
          <div class="metric-value"><?php echo number_format($factorAusencia, 1, '.', ','); ?>%</div>
          <div class="metric-label">Factor Ausencia</div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="bi bi-umbrella"></i>
          </div>
          <div class="metric-value"><?php echo number_format($factorVacaciones, 1, '.', ','); ?>%</div>
          <div class="metric-label">Factor Vacaciones</div>
        </div>
      </div>


    </div>

    <?php
    // Cerrar la conexión a la base de datos
    $db->cleanup();
    ?>

<!-- End of metrics sections -->

    <!-- Incluir los formularios específicos de Revisión y Soporte (excluyendo el de Materiales) -->
  </div> <!-- Cierre del div.app-container -->

  <!-- Formulario de Revisión -->
  <div class="form-panel" id="revisionFormPanel">
    <div class="form-container">
      <div class="form-header">
        <h2 class="form-title"><i class="bi bi-clipboard-check"></i> Formulario de Revisión</h2>
        <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
      </div>
      <form id="revisionForm">
        <div class="form-group">
          <label for="tipo_revision" class="form-label">Tipo de Revisión</label>
          <select id="tipo_revision" class="form-select" required>
            <option value="" selected disabled>Seleccione el tipo de revisión</option>
            <option value="rutina">Rutina</option>
            <option value="especial">Especial</option>
              <option value="seguimiento">Seguimiento</option>
            </select>
          </div>

          <div class="form-group">
            <label for="fecha_revision" class="form-label">Fecha</label>
            <input type="date" id="fecha_revision" class="date-input" required>
          </div>

          <div class="form-group">
            <label for="hora_revision" class="form-label">Hora</label>
            <input type="time" id="hora_revision" class="form-control" required>
          </div>

          <div class="form-group">
            <label for="tecnico" class="form-label">RUT del Técnico</label>
            <input type="text" id="tecnico" class="form-control" placeholder="Ingrese el RUT del técnico" required value="<?php echo htmlspecialchars($rutTecnico); ?>">
          </div>

          <div class="form-group">
            <label for="equipo" class="form-label">Equipo/Sistema Revisado</label>
            <select id="equipo" class="form-select" required>
              <option value="" selected disabled>Seleccione el equipo/sistema</option>
              <option value="red_hfc">Red HFC</option>
              <option value="red_ftth">Red FTTH</option>
              <option value="equipos_cliente">Equipos de Cliente</option>
              <option value="nodos">Nodos</option>
              <option value="amplificadores">Amplificadores</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Lista de Verificación</label>
            <div class="form-check">
              <input type="checkbox" id="check1" class="form-check-input">
              <label for="check1" class="form-check-label">Conexiones físicas</label>
            </div>
            <div class="form-check">
              <input type="checkbox" id="check2" class="form-check-input">
              <label for="check2" class="form-check-label">Niveles de señal</label>
            </div>
            <div class="form-check">
              <input type="checkbox" id="check3" class="form-check-input">
              <label for="check3" class="form-check-label">Estado de equipos</label>
            </div>
            <div class="form-check">
              <input type="checkbox" id="check4" class="form-check-input">
              <label for="check4" class="form-check-label">Configuración</label>
            </div>
            <div class="form-check">
              <input type="checkbox" id="check5" class="form-check-input">
              <label for="check5" class="form-check-label">Pruebas de rendimiento</label>
            </div>
          </div>

          <div class="form-group">
            <label for="observaciones_revision" class="form-label">Observaciones</label>
            <textarea id="observaciones_revision" class="form-textarea" placeholder="Ingrese cualquier observación relevante" required></textarea>
          </div>

          <div class="form-buttons">
            <button type="button" class="btn-cancel">Cancelar</button>
            <button type="submit" class="btn-submit">Guardar</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Formulario de Soporte -->
    <div class="form-panel" id="soporteFormPanel">
      <div class="form-container">
        <div class="form-header">
          <h2 class="form-title"><i class="bi bi-headset"></i> Formulario de Soporte</h2>
          <button type="button" class="close-form-btn" title="Cerrar formulario" aria-label="Cerrar formulario"><i class="bi bi-x-lg"></i></button>
        </div>
        <form id="soporteForm">
          <div class="form-group">
            <label for="tipo_soporte" class="form-label">Tipo de Solicitud</label>
            <select id="tipo_soporte" class="form-select" required>
              <option value="" selected disabled>Seleccione el tipo de solicitud</option>
              <option value="problema_tecnico">Problema Técnico</option>
              <option value="solicitud_info">Solicitud de Información</option>
              <option value="reporte_falla">Reporte de Falla</option>
              <option value="mejora">Sugerencia de Mejora</option>
            </select>
          </div>

          <div class="form-group">
            <label for="prioridad" class="form-label">Nivel de Prioridad</label>
            <div class="radio-group">
              <div class="form-check">
                <input type="radio" id="prioridad_baja" name="prioridad" value="baja" class="form-check-input">
                <label for="prioridad_baja" class="form-check-label">Baja</label>
              </div>
              <div class="form-check">
                <input type="radio" id="prioridad_media" name="prioridad" value="media" class="form-check-input" checked>
                <label for="prioridad_media" class="form-check-label">Media</label>
              </div>
              <div class="form-check">
                <input type="radio" id="prioridad_alta" name="prioridad" value="alta" class="form-check-input">
                <label for="prioridad_alta" class="form-check-label">Alta</label>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="solicitante" class="form-label">RUT del Solicitante</label>
            <input type="text" id="solicitante" class="form-control" placeholder="Ingrese su RUT" required value="<?php echo htmlspecialchars($rutTecnico); ?>">
          </div>

          <div class="form-group">
            <label for="contacto" class="form-label">Información de Contacto</label>
            <input type="text" id="contacto" class="form-control" placeholder="Teléfono o correo electrónico" required>
          </div>

          <div class="form-group">
            <label for="descripcion" class="form-label">Descripción del Problema</label>
            <textarea id="descripcion" class="form-textarea" placeholder="Describa detalladamente el problema o solicitud" required></textarea>
          </div>

          <div class="form-buttons">
            <button type="button" class="btn-cancel">Cancelar</button>
            <button type="submit" class="btn-submit">Enviar Solicitud</button>
          </div>
        </form>
      </div>
    </div>

  <!-- Incluir el footer modular -->
  <?php include 'components/footer_modular.php'; ?>

  <!-- No need for hidden KPI data anymore as we're getting data directly from the database -->

  <!-- Incluir datos de sesión para JavaScript -->
  <script>
    // Pasar valores de PHP a JavaScript
    const phpData = {
      periodo: '<?php echo $periodo; ?>',
      rutTecnico: '<?php echo $rutTecnico; ?>',
      idSesion: '<?php echo $idSesion; ?>'
    };
  </script>

  <!-- Scripts -->
  <script src="js/activity_dashboard.js"></script>
  <script src="js/collapsible-sections.js"></script>
  <!-- Los scripts del footer modular manejan los formularios y menús -->

  <!-- El footer modular maneja automáticamente el elemento activo -->

  <?php
  // Incluir el script para mantener la sesión activa
  incluirScriptSesion();
  ?>

  <?php if (!empty($pendingDocs) || $is_pilot_user): ?>
  <div class="modal-overlay" id="hcmModal">
    <div class="modal-content">
      <!-- Botón de cierre (solo visible en el segundo mensaje) -->
      <span class="close-modal" id="closeModalBtn" aria-label="Cerrar" style="display: none;">&times;</span>

      <?php if ($is_pilot_user): ?>
      <!-- Mensaje del Piloto (solo para top 10 técnicos) -->
      <div class="modal-message active" id="pilotMessage">
        <h3>🚀 Has sido seleccionado para nuestro Programa Piloto</h3>
        <p>Estimado colaborador, nos complace informarte que ha sido liberada  <strong>una nueva interfaz del modulo Logístico</strong>.</p>
<!-- 
        <div class="pilot-highlight">
          <h4 style="margin-top: 0; color: #00e1fd;">✨ ¿Por qué fuiste seleccionado?</h4>
          <p style="margin-bottom: 0;">Tu destacado desempeño y experiencia en el manejo de materiales te convierte en un candidato ideal para evaluar y mejorar nuestra nueva interfaz logística.</p>
        </div> -->

        <h4>🎯 Objetivos del Piloto:</h4>
        <ul>
          <li>Evaluar la usabilidad de la nueva interfaz</li>
          <li>Identificar oportunidades de mejora</li>
          <li>Optimizar los procesos logísticos</li>
          <li>Recopilar feedback valioso para el desarrollo final</li>
        </ul>

        <p><strong>Tu participación es fundamental</strong> para el éxito de este proyecto. Valoramos enormemente tu experiencia y esperamos contar con tus comentarios, sugerencias y observaciones.</p>

        <a href="mod_logistica.php" class="pilot-link">🔗 Acceder al Nuevo Módulo Logístico</a>

        <p style="margin-top: 15px; font-size: 0.9rem; color: rgba(255, 255, 255, 0.8);">
          <strong>Nota:</strong> Puedes acceder al nuevo módulo en cualquier momento desde el botón de logística en el menú inferior.
        </p>
      </div>
      <?php endif; ?>

      <?php if (!empty($pendingDocs)): ?>
      <!-- Primer mensaje (solo si hay documentos pendientes) -->
      <div class="modal-message <?php echo $is_pilot_user ? '' : 'active'; ?>" id="message1">
        <h3>📢 Información Importante</h3>
        <p>Estimado técnico, antes de continuar con sus actividades, es importante que revise la siguiente información:</p>
        <div style="background: rgba(0, 225, 253, 0.1); padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #00e1fd;">
          <h4 style="margin-top: 0; color: #00e1fd;">🔔 Recordatorio del Sistema</h4>
          <p style="margin-bottom: 0;">Se han detectado documentos pendientes en su perfil que requieren su atención inmediata. Por favor, revise la información detallada en el siguiente paso.</p>
        </div>
        <p><strong>Nota:</strong> Es fundamental mantener su documentación al día para garantizar el correcto funcionamiento de sus actividades laborales.</p>
      </div>
      <?php endif; ?>

      <!-- Segundo mensaje (mensaje original HCM) -->
      <div class="modal-message" id="message2">
        <h3>¡Atención! Firmas pendientes en HCM</h3>
        <p>Por favor, regularice sus firmas pendientes en HCM a la brevedad.</p>
        <p>Si ya lo hizo, ignore este mensaje.</p>
        <h4>Documentos pendientes:</h4>
        <ul>
          <?php foreach ($pendingDocs as $doc): ?>
            <li><?php echo htmlspecialchars($doc); ?></li>
          <?php endforeach; ?>
        </ul>
      </div>

      <!-- Navegación del modal -->
      <div class="modal-navigation">
        <?php if ($is_pilot_user && !empty($pendingDocs)): ?>
        <!-- Navegación para usuarios piloto con documentos pendientes (3 pasos) -->
        <div class="modal-progress">
          <span class="modal-progress-dot active" id="dot1"></span>
          <span class="modal-progress-dot" id="dot2"></span>
          <span class="modal-progress-dot" id="dot3"></span>
          <span id="progressText">Paso 1 de 3</span>
        </div>
        <button class="modal-btn" id="nextBtn">Siguiente</button>
        <?php elseif ($is_pilot_user): ?>
        <!-- Navegación para usuarios piloto sin documentos pendientes (1 paso) -->
        <div class="modal-progress">
          <span class="modal-progress-dot active" id="dot1"></span>
          <span id="progressText">Información del Piloto</span>
        </div>
        <button class="modal-btn" id="nextBtn">Entendido</button>
        <?php elseif (!empty($pendingDocs)): ?>
        <!-- Navegación para usuarios normales con documentos pendientes (2 pasos) -->
        <div class="modal-progress">
          <span class="modal-progress-dot active" id="dot1"></span>
          <span class="modal-progress-dot" id="dot2"></span>
          <span id="progressText">Paso 1 de 2</span>
        </div>
        <button class="modal-btn" id="nextBtn">Siguiente</button>
        <?php endif; ?>
      </div>
    </div>
  </div>
  <?php endif; ?>

  <script>
    // Sistema de modal mejorado para piloto y documentos pendientes
    document.addEventListener('DOMContentLoaded', () => {
        console.log('Iniciando verificación de modal');
        console.log('Documentos pendientes:', <?php echo json_encode($pendingDocs); ?>);
        console.log('Usuario piloto:', <?php echo json_encode($is_pilot_user); ?>);

        const modal = document.getElementById('hcmModal');
        console.log('Modal encontrado:', !!modal);

        if (modal) {
            console.log('Inicializando sistema de modal');

            // Elementos del modal
            const pilotMessage = document.getElementById('pilotMessage');
            const message1 = document.getElementById('message1');
            const message2 = document.getElementById('message2');
            const nextBtn = document.getElementById('nextBtn');
            const closeBtn = document.getElementById('closeModalBtn');
            const dot1 = document.getElementById('dot1');
            const dot2 = document.getElementById('dot2');
            const dot3 = document.getElementById('dot3');
            const progressText = document.getElementById('progressText');

            // Configuración basada en el tipo de usuario
            const isPilotUser = <?php echo json_encode($is_pilot_user); ?>;
            const hasPendingDocs = <?php echo json_encode(!empty($pendingDocs)); ?>;

            let currentMessage = 1;
            let totalMessages = 1;

            if (isPilotUser && hasPendingDocs) {
                totalMessages = 3; // Piloto + Info + HCM
            } else if (isPilotUser) {
                totalMessages = 1; // Solo Piloto
            } else if (hasPendingDocs) {
                totalMessages = 2; // Info + HCM
            }

            // Función para mostrar mensaje específico
            function showMessage(messageNumber) {
                console.log('Mostrando mensaje:', messageNumber);

                // Ocultar todos los mensajes
                if (pilotMessage) pilotMessage.classList.remove('active');
                if (message1) message1.classList.remove('active');
                if (message2) message2.classList.remove('active');

                // Resetear dots
                if (dot1) dot1.classList.remove('active');
                if (dot2) dot2.classList.remove('active');
                if (dot3) dot3.classList.remove('active');

                // Mostrar el mensaje correspondiente según el flujo
                if (isPilotUser && hasPendingDocs) {
                    // Flujo: Piloto -> Info -> HCM
                    if (messageNumber === 1 && pilotMessage) {
                        pilotMessage.classList.add('active');
                        currentMessage = 1;
                        nextBtn.textContent = 'Siguiente';
                        closeBtn.style.display = 'none';
                        if (dot1) dot1.classList.add('active');
                        if (progressText) progressText.textContent = 'Paso 1 de 3';
                    } else if (messageNumber === 2 && message1) {
                        message1.classList.add('active');
                        currentMessage = 2;
                        nextBtn.textContent = 'Siguiente';
                        closeBtn.style.display = 'none';
                        if (dot2) dot2.classList.add('active');
                        if (progressText) progressText.textContent = 'Paso 2 de 3';
                    } else if (messageNumber === 3 && message2) {
                        message2.classList.add('active');
                        currentMessage = 3;
                        nextBtn.textContent = 'Cerrar';
                        closeBtn.style.display = 'block';
                        if (dot3) dot3.classList.add('active');
                        if (progressText) progressText.textContent = 'Paso 3 de 3';
                    }
                } else if (isPilotUser) {
                    // Solo mensaje del piloto
                    if (messageNumber === 1 && pilotMessage) {
                        pilotMessage.classList.add('active');
                        currentMessage = 1;
                        nextBtn.textContent = 'Entendido';
                        closeBtn.style.display = 'block';
                        if (dot1) dot1.classList.add('active');
                        if (progressText) progressText.textContent = 'Información del Piloto';
                    }
                } else if (hasPendingDocs) {
                    // Flujo normal: Info -> HCM
                    if (messageNumber === 1 && message1) {
                        message1.classList.add('active');
                        currentMessage = 1;
                        nextBtn.textContent = 'Siguiente';
                        closeBtn.style.display = 'none';
                        if (dot1) dot1.classList.add('active');
                        if (progressText) progressText.textContent = 'Paso 1 de 2';
                    } else if (messageNumber === 2 && message2) {
                        message2.classList.add('active');
                        currentMessage = 2;
                        nextBtn.textContent = 'Cerrar';
                        closeBtn.style.display = 'block';
                        if (dot2) dot2.classList.add('active');
                        if (progressText) progressText.textContent = 'Paso 2 de 2';
                    }
                }
            }

            // Función para cerrar el modal
            function closeModal() {
                console.log('Cerrando modal');
                modal.style.display = 'none';
            }

            // Event listener para el botón siguiente/cerrar
            nextBtn.addEventListener('click', () => {
                console.log('Botón siguiente clickeado, mensaje actual:', currentMessage, 'Total:', totalMessages);

                if (currentMessage < totalMessages) {
                    console.log('Avanzando al mensaje', currentMessage + 1);
                    showMessage(currentMessage + 1);
                } else {
                    console.log('Cerrando modal desde botón');
                    closeModal();
                }
            });

            // Event listener para el botón de cierre (X)
            closeBtn.addEventListener('click', () => {
                console.log('Cerrar modal clickeado');
                closeModal();
            });

            // Prevenir cierre del modal al hacer clic fuera (solo en el último mensaje)
            modal.addEventListener('click', (e) => {
                if (e.target === modal && currentMessage === totalMessages) {
                    console.log('Clic fuera del modal - cerrando');
                    closeModal();
                } else if (e.target === modal && currentMessage < totalMessages) {
                    console.log('Clic fuera del modal - no es el último mensaje, no se cierra');
                }
            });

            // Prevenir cierre con tecla Escape (solo en el último mensaje)
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && modal.style.display === 'flex') {
                    if (currentMessage === totalMessages) {
                        console.log('Escape presionado - cerrando modal');
                        closeModal();
                    } else {
                        console.log('Escape presionado - no es el último mensaje, no se cierra');
                    }
                }
            });

            // Mostrar el modal con el primer mensaje
            showMessage(1);
            modal.style.display = 'flex';
            modal.style.opacity = '1';
            modal.querySelector('.modal-content').style.display = 'block';
            modal.querySelector('.modal-content').style.opacity = '1';
            console.log('Modal mostrado con primer mensaje');
        } else {
            console.log('No se encontró el modal o no hay documentos pendientes');
        }

        // Mostrar información de depuración
        console.log('Información de depuración:', <?php echo json_encode($debugInfo); ?>);
    });
  </script>
</body>
</html>
